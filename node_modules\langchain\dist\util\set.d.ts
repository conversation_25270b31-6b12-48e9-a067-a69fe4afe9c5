/**
 * Source: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Set#implementing_basic_set_operations
 */
/**
 * returns intersection of two sets
 */
export declare function intersection<T>(setA: Set<T>, setB: Set<T>): Set<T>;
/**
 * returns union of two sets
 */
export declare function union<T>(setA: Set<T>, setB: Set<T>): Set<T>;
/**
 * returns difference of two sets
 */
export declare function difference<T>(setA: Set<T>, setB: Set<T>): Set<T>;
