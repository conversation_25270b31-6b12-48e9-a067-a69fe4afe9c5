"use strict";
/* tslint:disable */
/* eslint-disable */
/**
 * Pinecone Control Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2025-01
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollectionModelToJSON = exports.CollectionModelFromJSONTyped = exports.CollectionModelFromJSON = exports.instanceOfCollectionModel = exports.CollectionModelStatusEnum = void 0;
const runtime_1 = require("../runtime");
/**
 * @export
 */
exports.CollectionModelStatusEnum = {
    Initializing: 'Initializing',
    Ready: 'Ready',
    Terminating: 'Terminating'
};
/**
 * Check if a given object implements the CollectionModel interface.
 */
function instanceOfCollectionModel(value) {
    let isInstance = true;
    isInstance = isInstance && "name" in value;
    isInstance = isInstance && "status" in value;
    isInstance = isInstance && "environment" in value;
    return isInstance;
}
exports.instanceOfCollectionModel = instanceOfCollectionModel;
function CollectionModelFromJSON(json) {
    return CollectionModelFromJSONTyped(json, false);
}
exports.CollectionModelFromJSON = CollectionModelFromJSON;
function CollectionModelFromJSONTyped(json, ignoreDiscriminator) {
    if ((json === undefined) || (json === null)) {
        return json;
    }
    return {
        'name': json['name'],
        'size': !(0, runtime_1.exists)(json, 'size') ? undefined : json['size'],
        'status': json['status'],
        'dimension': !(0, runtime_1.exists)(json, 'dimension') ? undefined : json['dimension'],
        'vectorCount': !(0, runtime_1.exists)(json, 'vector_count') ? undefined : json['vector_count'],
        'environment': json['environment'],
    };
}
exports.CollectionModelFromJSONTyped = CollectionModelFromJSONTyped;
function CollectionModelToJSON(value) {
    if (value === undefined) {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    return {
        'name': value.name,
        'size': value.size,
        'status': value.status,
        'dimension': value.dimension,
        'vector_count': value.vectorCount,
        'environment': value.environment,
    };
}
exports.CollectionModelToJSON = CollectionModelToJSON;
//# sourceMappingURL=CollectionModel.js.map