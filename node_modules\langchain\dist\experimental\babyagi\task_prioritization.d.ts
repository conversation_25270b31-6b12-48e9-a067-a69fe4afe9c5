import { LL<PERSON>hain, LLMChainInput } from "../../chains/llm_chain.js";
/** Chain to prioritize tasks. */
export declare class TaskPrioritizationChain extends LLMChain {
    static lc_name(): string;
    /**
     * Static method to create a new TaskPrioritizationChain from a
     * BaseLanguageModel. It generates a prompt using the PromptTemplate class
     * and the task prioritization template, and returns a new instance of
     * TaskPrioritizationChain.
     * @param fields Object with fields used to initialize the chain, excluding the prompt.
     * @returns A new instance of TaskPrioritizationChain.
     */
    static fromLLM(fields: Omit<LLMChainInput, "prompt">): LLMChain;
}
