import { BaseLanguageModelInput, ToolDefinition } from "@langchain/core/language_models/base";
import { BaseChatModel, BaseChatModelParams, BindToolsInput, type BaseChatModelCallOptions } from "@langchain/core/language_models/chat_models";
import { BaseMessage, type AIMessageChunk } from "@langchain/core/messages";
import { type RunnableBatchOptions, RunnableBinding, type RunnableConfig, type RunnableToolLike } from "@langchain/core/runnables";
import { IterableReadableStream } from "@langchain/core/utils/stream";
import { type LogStreamCallbackHandlerInput, type RunLogPatch, type StreamEvent } from "@langchain/core/tracers/log_stream";
import { type StructuredToolInterface } from "@langchain/core/tools";
import { CallbackManagerForLLMRun } from "@langchain/core/callbacks/manager";
import { ChatResult } from "@langchain/core/outputs";
interface EventStreamCallbackHandlerInput extends Omit<LogStreamCallbackHandlerInput, "_schemaFormat"> {
}
declare const _SUPPORTED_PROVIDERS: readonly ["openai", "anthropic", "azure_openai", "cohere", "google-vertexai", "google-vertexai-web", "google-genai", "ollama", "together", "fireworks", "mistralai", "groq", "bedrock", "cerebras", "deepseek", "xai"];
export type ChatModelProvider = (typeof _SUPPORTED_PROVIDERS)[number];
export interface ConfigurableChatModelCallOptions extends BaseChatModelCallOptions {
    tools?: (StructuredToolInterface | Record<string, unknown> | ToolDefinition | RunnableToolLike)[];
}
/**
 * Attempts to infer the model provider based on the given model name.
 *
 * @param {string} modelName - The name of the model to infer the provider for.
 * @returns {string | undefined} The inferred model provider name, or undefined if unable to infer.
 *
 * @example
 * _inferModelProvider("gpt-4"); // returns "openai"
 * _inferModelProvider("claude-2"); // returns "anthropic"
 * _inferModelProvider("unknown-model"); // returns undefined
 */
export declare function _inferModelProvider(modelName: string): string | undefined;
interface ConfigurableModelFields extends BaseChatModelParams {
    defaultConfig?: Record<string, any>;
    /**
     * @default "any"
     */
    configurableFields?: string[] | "any";
    /**
     * @default ""
     */
    configPrefix?: string;
    /**
     * Methods which should be called after the model is initialized.
     * The key will be the method name, and the value will be the arguments.
     */
    queuedMethodOperations?: Record<string, any>;
}
/**
 * Internal class used to create chat models.
 *
 * @internal
 */
export declare class ConfigurableModel<RunInput extends BaseLanguageModelInput = BaseLanguageModelInput, CallOptions extends ConfigurableChatModelCallOptions = ConfigurableChatModelCallOptions> extends BaseChatModel<CallOptions, AIMessageChunk> {
    _llmType(): string;
    lc_namespace: string[];
    _defaultConfig?: Record<string, any>;
    /**
     * @default "any"
     */
    _configurableFields: string[] | "any";
    /**
     * @default ""
     */
    _configPrefix: string;
    /**
     * Methods which should be called after the model is initialized.
     * The key will be the method name, and the value will be the arguments.
     */
    _queuedMethodOperations: Record<string, any>;
    constructor(fields: ConfigurableModelFields);
    _model(config?: RunnableConfig): Promise<BaseChatModel<BaseChatModelCallOptions, AIMessageChunk>>;
    _generate(messages: BaseMessage[], options?: this["ParsedCallOptions"], runManager?: CallbackManagerForLLMRun): Promise<ChatResult>;
    bindTools(tools: BindToolsInput[], params?: Record<string, any>): ConfigurableModel<RunInput, CallOptions>;
    withStructuredOutput: BaseChatModel["withStructuredOutput"];
    _modelParams(config?: RunnableConfig): Record<string, any>;
    _removePrefix(str: string, prefix: string): string;
    /**
     * Bind config to a Runnable, returning a new Runnable.
     * @param {RunnableConfig | undefined} [config] - The config to bind.
     * @returns {RunnableBinding<RunInput, RunOutput, CallOptions>} A new RunnableBinding with the bound config.
     */
    withConfig(config?: RunnableConfig): RunnableBinding<RunInput, AIMessageChunk, CallOptions>;
    invoke(input: RunInput, options?: CallOptions): Promise<AIMessageChunk>;
    stream(input: RunInput, options?: CallOptions): Promise<IterableReadableStream<AIMessageChunk>>;
    batch(inputs: RunInput[], options?: Partial<CallOptions> | Partial<CallOptions>[], batchOptions?: RunnableBatchOptions & {
        returnExceptions?: false;
    }): Promise<AIMessageChunk[]>;
    batch(inputs: RunInput[], options?: Partial<CallOptions> | Partial<CallOptions>[], batchOptions?: RunnableBatchOptions & {
        returnExceptions: true;
    }): Promise<(AIMessageChunk | Error)[]>;
    batch(inputs: RunInput[], options?: Partial<CallOptions> | Partial<CallOptions>[], batchOptions?: RunnableBatchOptions): Promise<(AIMessageChunk | Error)[]>;
    transform(generator: AsyncGenerator<RunInput>, options: CallOptions): AsyncGenerator<AIMessageChunk>;
    streamLog(input: RunInput, options?: Partial<CallOptions>, streamOptions?: Omit<LogStreamCallbackHandlerInput, "autoClose">): AsyncGenerator<RunLogPatch>;
    streamEvents(input: RunInput, options: Partial<CallOptions> & {
        version: "v1" | "v2";
    }, streamOptions?: Omit<EventStreamCallbackHandlerInput, "autoClose">): IterableReadableStream<StreamEvent>;
    streamEvents(input: RunInput, options: Partial<CallOptions> & {
        version: "v1" | "v2";
        encoding: "text/event-stream";
    }, streamOptions?: Omit<EventStreamCallbackHandlerInput, "autoClose">): IterableReadableStream<Uint8Array>;
}
export interface InitChatModelFields extends Partial<Record<string, any>> {
    modelProvider?: string;
    configurableFields?: string[] | "any";
    configPrefix?: string;
}
export type ConfigurableFields = "any" | string[];
export declare function initChatModel<RunInput extends BaseLanguageModelInput = BaseLanguageModelInput, CallOptions extends ConfigurableChatModelCallOptions = ConfigurableChatModelCallOptions>(model: string, fields?: Partial<Record<string, any>> & {
    modelProvider?: string;
    configurableFields?: never;
    configPrefix?: string;
}): Promise<ConfigurableModel<RunInput, CallOptions>>;
export declare function initChatModel<RunInput extends BaseLanguageModelInput = BaseLanguageModelInput, CallOptions extends ConfigurableChatModelCallOptions = ConfigurableChatModelCallOptions>(model: never, options?: Partial<Record<string, any>> & {
    modelProvider?: string;
    configurableFields?: never;
    configPrefix?: string;
}): Promise<ConfigurableModel<RunInput, CallOptions>>;
export declare function initChatModel<RunInput extends BaseLanguageModelInput = BaseLanguageModelInput, CallOptions extends ConfigurableChatModelCallOptions = ConfigurableChatModelCallOptions>(model?: string, options?: Partial<Record<string, any>> & {
    modelProvider?: string;
    configurableFields?: ConfigurableFields;
    configPrefix?: string;
}): Promise<ConfigurableModel<RunInput, CallOptions>>;
export {};
